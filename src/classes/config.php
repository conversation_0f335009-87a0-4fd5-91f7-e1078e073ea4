<?php

class Config
{
	public string  $ID;
	public float   $trm;
	public string  $n_accountconcejo;
	public float   $trk_income_gbp;
	public float   $trkincomeusdgbp;
	public float   $trkincomeusd;
	public float   $trkincomeproyectadousdgbp;
	public float   $trkincomeproyectadousd;
	public float   $trk_income_deuda;
	public float   $trk_income_fee_transfer_paypal_usd;
	public float   $trk_income_fee_transfer_paypal_gbp;
	public float   $trk_income_fee_transfer_paypal_total;
	public float   $trk_income_fee_porc_nequi;
	public float   $trk_income_fee_valor_nequi;
	public float   $trk_income_iva_porc_comision_nequi;
	public float   $trk_income_iva_valor_comision_nequi;
	public float   $trk_income_total_acumulado;
	public float   $trk_income_total_acumulado_menos_deuda;
	public float   $trk_income_total_acumulado_menos_fee;
	public float   $trk_income_total_acumulado_menos_fee_paypal;
	public float   $trk_income_total_acumulado_cop;
	public float   $trk_income_total_acumulado_cop_menos_nequi;
	public float   $trk_income_trm_nequi;
	public float   $porcfeefrank;
	public float   $feetransfer;
	public float   $total;
	public float   $totalproyectado;
	public float   $totalsoloproyectado;
	public float   $valfeefrank;
	public float   $valfeefrankproyectado;
	public float   $valprofit;
	public float   $valprofitproyectado;
	public float   $valnetprofit;
	public float   $valnetprofitproyectado;
	public float   $valprofitcop;
	public float   $valprofitcopproyectado;
	public float   $valmin_valor_apuesta;
	public float   $betbankroll;
	public float   $riskkellyformula;
	public float   $valmin_prediccion;
	public float   $riesgo_diario;
	public ?string $fecha_revision_betplay;
	public float   $val_min_avg_all;
	public float   $val_min_avg_specific;
	public float   $val_penalidad_mitad_ultimos_partidos;
	public float   $val_penalidad_resto_ultimos_partidos;
	public float   $val_penalidad_same_fixture;
	public float   $val_penalidad_1ro_ultimos_partidos;
	public float   $val_penalidad_2do_ultimos_partidos;
	public float   $val_penalidad_3ro_ultimos_partidos;
	public float   $min_perc_kelly_crit;
	public float   $min_odds;
	public float   $perc_penalidad_avg_ult_partidos;
	public float   $bt_bank;
	public float   $bt_porc_apuesta_min;
	public float   $bt_min_porc_profit;
	public float   $bt_min_porc_bank;
	public string  $trk_id;
	public float   $trk_meta_diaria;
	public float   $trk_meta_semanal;
	public float   $trk_meta_mensual;
	public float   $trk_fee;
	public float   $bet_valor_apostado_default;
	public ?string $bet_season_1;
	public ?string $bet_season_2;

	private string $bd_table                                = 'configs';
	private string $bd_alias                                = 'confi';
	private string $bd_id                                   = 'id_config';
	private string $bd_trm                                  = 'trm';
	private string $bd_naccountconcejo                      = 'n_accountconcejo';
	private string $bd_trk_income_gbp                       = 'trk_income_gbp';
	private string $bd_trkincomeusdgbp                      = 'trk_income_usdgbp';
	private string $bd_trkincomeusd                         = 'trk_income_usd';
	private string $bd_trkincomeproyectadousdgbp            = 'trk_income_proyectado_usdgbp';
	private string $bd_trkincomeproyectadousd               = 'trk_income_proyectado_usd';
	private string $bd_trk_income_deuda                     = 'trk_income_deuda';
	private string $bd_trk_income_fee_transfer_paypal_usd   = 'trk_income_fee_transfer_paypal_usd';
	private string $bd_trk_income_fee_transfer_paypal_gbp   = 'trk_income_fee_transfer_paypal_gbp';
	private string $bd_trk_income_fee_porc_nequi            = 'trk_income_fee_porc_nequi';
	private string $bd_trk_income_iva_porc_comision_nequi   = 'trk_income_iva_porc_comision_nequi';
	private string $bd_porcfeefrank                         = 'porc_fee_frank';
	private string $bd_feetransfer                          = 'fee_transfer';
	private string $bd_valmin_valor_apuesta                 = 'valmin_valor_apuesta';
	private string $bd_betbankroll                          = 'bet_bankroll';
	private string $bd_riskkellyformula                     = 'risk_kellyformula';
	private string $bd_valmin_prediccion                    = 'valmin_prediccion';
	private string $bd_riesgo_diario                        = 'riesgo_diario';
	private string $bd_fecha_revision_betplay               = 'fecha_revision_betplay';
	private string $bd_val_min_avg_all                      = 'val_min_avg_all';
	private string $bd_val_min_avg_specific                 = 'val_min_avg_specific';
	private string $bd_val_penalidad_mitad_ultimos_partidos = 'val_penalidad_mitad_ultimos_partidos';
	private string $bd_val_penalidad_resto_ultimos_partidos = 'val_penalidad_resto_ultimos_partidos';
	private string $bd_val_penalidad_same_fixture           = 'val_penalidad_same_fixture';
	private string $bd_val_penalidad_1ro_ultimos_partidos   = 'val_penalidad_1ro_ultimos_partidos';
	private string $bd_val_penalidad_2do_ultimos_partidos   = 'val_penalidad_2do_ultimos_partidos';
	private string $bd_val_penalidad_3ro_ultimos_partidos   = 'val_penalidad_3ro_ultimos_partidos';
	private string $bd_min_perc_kelly_crit                  = 'min_perc_kelly_crit';
	private string $bd_min_odds                             = 'min_odds';
	private string $bd_perc_penalidad_avg_ult_partidos      = 'perc_penalidad_avg_ult_partidos';
	private string $bd_bt_bank                              = 'bt_bank';
	private string $bd_bt_porc_apuesta_min                  = 'bt_porc_apuesta_min';
	private string $bd_bt_min_porc_profit                   = 'bt_min_porc_profit';
	private string $bd_bt_min_porc_bank                     = 'bt_min_porc_bank';
	private string $bd_trk_meta_diaria                      = 'trk_meta_diaria';
	private string $bd_trk_meta_semanal                     = 'trk_meta_semanal';
	private string $bd_trk_meta_mensual                     = 'trk_meta_mensual';
	private string $bd_trk_fee                              = 'trk_fee';
	private string $bd_bet_valor_apostado_default           = 'bet_valor_apostado_default';
	private string $bd_bet_season_1                         = 'bet_season_1';
	private string $bd_bet_season_2                         = 'bet_season_2';

	function __construct()
	{
		$this->ID                                          = '';
		$this->trm                                         = 0;
		$this->n_accountconcejo                            = '';
		$this->trk_income_gbp                              = 0;
		$this->trkincomeusdgbp                             = 0;
		$this->trkincomeusd                                = 0;
		$this->trkincomeproyectadousdgbp                   = 0;
		$this->trkincomeproyectadousd                      = 0;
		$this->trk_income_deuda                            = 0;
		$this->trk_income_fee_transfer_paypal_usd          = 0;
		$this->trk_income_fee_transfer_paypal_gbp          = 0;
		$this->trk_income_fee_transfer_paypal_total        = 0;
		$this->trk_income_fee_porc_nequi                   = 0;
		$this->trk_income_fee_valor_nequi                  = 0;
		$this->trk_income_iva_porc_comision_nequi          = 0;
		$this->trk_income_iva_valor_comision_nequi         = 0;
		$this->trk_income_total_acumulado                  = 0;
		$this->trk_income_total_acumulado_menos_deuda      = 0;
		$this->trk_income_total_acumulado_menos_fee        = 0;
		$this->trk_income_total_acumulado_menos_fee_paypal = 0;
		$this->trk_income_total_acumulado_cop              = 0;
		$this->trk_income_total_acumulado_cop_menos_nequi  = 0;
		$this->trk_income_trm_nequi                        = 0;
		$this->porcfeefrank                                = 0;
		$this->feetransfer                                 = 0;
		$this->total                                       = 0;
		$this->valfeefrank                                 = 0;
		$this->valprofit                                   = 0;
		$this->valnetprofit                                = 0;
		$this->valprofitcop                                = 0;
		$this->valmin_valor_apuesta                        = 0;
		$this->betbankroll                                 = 0;
		$this->riskkellyformula                            = 0;
		$this->valmin_prediccion                           = 0;
		$this->riesgo_diario                               = 0;
		$this->fecha_revision_betplay                      = '';
		$this->val_min_avg_all                             = 0;
		$this->val_min_avg_specific                        = 0;
		$this->val_penalidad_mitad_ultimos_partidos        = 0;
		$this->val_penalidad_resto_ultimos_partidos        = 0;
		$this->val_penalidad_same_fixture                  = 0;
		$this->val_penalidad_1ro_ultimos_partidos          = 0;
		$this->val_penalidad_2do_ultimos_partidos          = 0;
		$this->val_penalidad_3ro_ultimos_partidos          = 0;
		$this->min_perc_kelly_crit                         = 0;
		$this->min_odds                                    = 0;
		$this->perc_penalidad_avg_ult_partidos             = 0;
		$this->bt_bank                                     = 0;
		$this->bt_porc_apuesta_min                         = 0;
		$this->bt_min_porc_profit                          = 0;
		$this->bt_min_porc_bank                            = 0;
		$this->trk_id                                      = '';
		$this->trk_meta_diaria                             = 0;
		$this->trk_meta_semanal                            = 0;
		$this->trk_meta_mensual                            = 0;
		$this->trk_fee                                     = 0;
		$this->bet_valor_apostado_default                  = 0;
		$this->bet_season_1                                = '';
		$this->bet_season_2                                = '';

	}
	
	/**
	 * @param $resultado
	 *
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$cq = new self;
			
			$objeto                                       = new self;
			$objeto->ID                                   = desordena($resultado[$cq->bd_id]);
			$objeto->trm                                  = $resultado[$cq->bd_trm];
			$objeto->n_accountconcejo                     = $resultado[$cq->bd_naccountconcejo];
			$objeto->trk_income_gbp                       = $resultado[$cq->bd_trk_income_gbp];
			$objeto->trkincomeusdgbp                      = $resultado[$cq->bd_trkincomeusdgbp];
			$objeto->trkincomeusd                         = $resultado[$cq->bd_trkincomeusd];
			$objeto->trkincomeproyectadousdgbp            = $resultado[$cq->bd_trkincomeproyectadousdgbp];
			$objeto->trkincomeproyectadousd               = $resultado[$cq->bd_trkincomeproyectadousd];
			$objeto->trk_income_deuda                     = $resultado[$cq->bd_trk_income_deuda];
			$objeto->trk_income_fee_transfer_paypal_usd   = $resultado[$cq->bd_trk_income_fee_transfer_paypal_usd];
			$objeto->trk_income_fee_transfer_paypal_gbp   = $resultado[$cq->bd_trk_income_fee_transfer_paypal_gbp];
			$objeto->trk_income_fee_porc_nequi            = $resultado[$cq->bd_trk_income_fee_porc_nequi];
			$objeto->trk_income_iva_porc_comision_nequi   = $resultado[$cq->bd_trk_income_iva_porc_comision_nequi];
			$objeto->trk_income_trm_nequi                 = $resultado['trk_income_trm_nequi'];
			$objeto->porcfeefrank                         = $resultado[$cq->bd_porcfeefrank];
			$objeto->feetransfer                          = $resultado[$cq->bd_feetransfer];
			$objeto->valmin_valor_apuesta                 = $resultado[$cq->bd_valmin_valor_apuesta];
			$objeto->betbankroll                          = $resultado[$cq->bd_betbankroll];
			$objeto->riskkellyformula                     = $resultado[$cq->bd_riskkellyformula];
			$objeto->valmin_prediccion                    = $resultado[$cq->bd_valmin_prediccion];
			$objeto->riesgo_diario                        = $resultado[$cq->bd_riesgo_diario];
			$objeto->fecha_revision_betplay               = $resultado[$cq->bd_fecha_revision_betplay];
			$objeto->val_min_avg_all                      = $resultado[$cq->bd_val_min_avg_all];
			$objeto->val_min_avg_specific                 = $resultado[$cq->bd_val_min_avg_specific];
			$objeto->val_penalidad_mitad_ultimos_partidos = $resultado[$cq->bd_val_penalidad_mitad_ultimos_partidos];
			$objeto->val_penalidad_resto_ultimos_partidos = $resultado[$cq->bd_val_penalidad_resto_ultimos_partidos];
			$objeto->val_penalidad_same_fixture           = $resultado[$cq->bd_val_penalidad_same_fixture];
			$objeto->val_penalidad_1ro_ultimos_partidos   = $resultado[$cq->bd_val_penalidad_1ro_ultimos_partidos];
			$objeto->val_penalidad_2do_ultimos_partidos   = $resultado[$cq->bd_val_penalidad_2do_ultimos_partidos];
			$objeto->val_penalidad_3ro_ultimos_partidos   = $resultado[$cq->bd_val_penalidad_3ro_ultimos_partidos];
			$objeto->min_perc_kelly_crit                  = $resultado[$cq->bd_min_perc_kelly_crit];
			$objeto->min_odds                             = $resultado[$cq->bd_min_odds];
			$objeto->perc_penalidad_avg_ult_partidos      = $resultado[$cq->bd_perc_penalidad_avg_ult_partidos];
			$objeto->bt_bank                              = $resultado[$cq->bd_bt_bank];
			$objeto->bt_porc_apuesta_min                  = $resultado[$cq->bd_bt_porc_apuesta_min];
			$objeto->bt_min_porc_profit                   = $resultado[$cq->bd_bt_min_porc_profit];
			$objeto->bt_min_porc_bank                     = $resultado[$cq->bd_bt_min_porc_bank];
			$objeto->trk_id                               = $resultado["trk_id"];
			$objeto->trk_meta_diaria                      = $resultado[$cq->bd_trk_meta_diaria];
			$objeto->trk_meta_semanal                     = $resultado[$cq->bd_trk_meta_semanal];
			$objeto->trk_meta_mensual                     = $resultado[$cq->bd_trk_meta_mensual];
			$objeto->trk_fee                              = $resultado[$cq->bd_trk_fee];
			$objeto->bet_valor_apostado_default           = $resultado[$cq->bd_bet_valor_apostado_default];
			$objeto->calculateTotal();
			$objeto->calculateTotalProyectado();
			$objeto->calculateValFee();
			$objeto->calculateValFeeProyectado();
			$objeto->calculateProfit();
			$objeto->calculateProfitProyectado();
			$objeto->calculateNetProfit();
			$objeto->calculateNetProfitProyectado();
			$objeto->calculateProfitCop();
			$objeto->calculateProfitCopProyectado();
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($conexion): self
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cqa ";
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	public static function get_trm($conexion)
	{
		try {
			$statement = $conexion->prepare(
				'SELECT * FROM configs LIMIT 1'
			);
			$statement->execute();
			
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return $resultado['trm'];
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	public static function get_n_accountconcejo($conexion)
	{
		try {
			$statement = $conexion->prepare(
				'SELECT * FROM configs LIMIT 1'
			);
			$statement->execute();
			
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return ordena($resultado['n_accountconcejo']);
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	private static function calculate_days_passed($datestartselfcare)
	{
		$datetime1 = new DateTime($datestartselfcare . '00:00:01');
		$datetime2 = new DateTime(create_datetime());
		
		$interval = date_diff($datetime1, $datetime2);
		
		return $interval->format('%R%a');
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_all_configs_page($conexion): Config
	{
		try {
			$statement = $conexion->prepare(
				'SELECT * FROM configs LIMIT 1'
			);
			$statement->execute();
			
			$resultado = $statement->fetch();
			
			if ($resultado) {
				$config                    = new Config;
				$config->trm               = $resultado['trm'];
				$config->trk_id            = $resultado['trk_id'];
				$config->trk_meta_diaria   = $resultado['trk_meta_diaria'];
				$config->trk_meta_semanal  = $resultado['trk_meta_semanal'];
				$config->trk_meta_mensual  = $resultado['trk_meta_mensual'];
				$config->trk_fee                      = $resultado['trk_fee'];
				$config->bet_valor_apostado_default   = $resultado['bet_valor_apostado_default'];
				$config->bet_season_1                 = $resultado['bet_season_1'];
				$config->bet_season_2                 = $resultado['bet_season_2'];

				return $config;
				
			} else {
				return new Config;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getTrkId($conexion): string
	{
		try {
			$statement = $conexion->prepare(
				'SELECT trk_id FROM configs WHERE id_config = 1'
			);
			$statement->execute();

			$resultado = $statement->fetch();

			if ($resultado) {
				return $resultado['trk_id'];

			} else {
				return '';
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}

	/**
	 * @throws Exception
	 */
	public static function getDailyGoal($conexion): float
	{
		try {
			$statement = $conexion->prepare(
				'SELECT trk_meta_diaria FROM configs WHERE id_config = 1'
			);
			$statement->execute();

			$resultado = $statement->fetch();

			if ($resultado) {
				return (float)$resultado['trk_meta_diaria'];

			} else {
				return 0.0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}

	/**
	 * @throws Exception
	 */
	public static function getWeeklyGoal($conexion): float
	{
		try {
			$statement = $conexion->prepare(
				'SELECT trk_meta_semanal FROM configs WHERE id_config = 1'
			);
			$statement->execute();

			$resultado = $statement->fetch();

			if ($resultado) {
				return (float)$resultado['trk_meta_semanal'];

			} else {
				return 0.0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}

	/**
	 * @throws Exception
	 */
	public static function getMonthlyGoal($conexion): float
	{
		try {
			$statement = $conexion->prepare(
				'SELECT trk_meta_mensual FROM configs WHERE id_config = 1'
			);
			$statement->execute();

			$resultado = $statement->fetch();

			if ($resultado) {
				return (float)$resultado['trk_meta_mensual'];

			} else {
				return 0.0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modify($conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_trm = :$cq->bd_trm ";
			$query .= "  ,$cq->bd_trkincomeusdgbp = :$cq->bd_trkincomeusdgbp ";
			$query .= "  ,$cq->bd_trkincomeusd = :$cq->bd_trkincomeusd ";
			$query .= "  ,$cq->bd_trkincomeproyectadousdgbp = :$cq->bd_trkincomeproyectadousdgbp ";
			$query .= "  ,$cq->bd_trkincomeproyectadousd = :$cq->bd_trkincomeproyectadousd ";
			$query .= "  ,$cq->bd_porcfeefrank = :$cq->bd_porcfeefrank ";
			$query .= "  ,$cq->bd_feetransfer = :$cq->bd_feetransfer ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_trm", $this->trm);
			$statement->bindValue(":$cq->bd_trkincomeusdgbp", $this->trkincomeusdgbp);
			$statement->bindValue(":$cq->bd_trkincomeusd", $this->trkincomeusd);
			$statement->bindValue(":$cq->bd_trkincomeproyectadousdgbp", $this->trkincomeproyectadousdgbp);
			$statement->bindValue(":$cq->bd_trkincomeproyectadousd", $this->trkincomeproyectadousd);
			$statement->bindValue(":$cq->bd_porcfeefrank", $this->porcfeefrank);
			$statement->bindValue(":$cq->bd_feetransfer", $this->feetransfer);
			$statement->bindValue(":$cq->bd_id", 1);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modificar_calcular_trk_income_paypal(PDO $conexion): void
	{
		try {
			$query = <<<SQL
						UPDATE configs SET
							 trk_income_deuda = :trk_income_deuda
							,porc_fee_frank = :porc_fee_frank
							,trk_income_gbp = :trk_income_gbp
							,trk_income_usdgbp = :trk_income_usdgbp
							,trk_income_usd = :trk_income_usd
							,trk_income_fee_transfer_paypal_usd = :trk_income_fee_transfer_paypal_usd
							,trk_income_fee_transfer_paypal_gbp = :trk_income_fee_transfer_paypal_gbp
							,trk_income_fee_porc_nequi = :trk_income_fee_porc_nequi
							,trk_income_iva_porc_comision_nequi = :trk_income_iva_porc_comision_nequi
							,trk_income_trm_nequi = :trk_income_trm_nequi
						WHERE
							id_config = :id_config
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":trk_income_deuda", $this->trk_income_deuda);
			$statement->bindValue("porc_fee_frank", $this->porcfeefrank);
			$statement->bindValue("trk_income_gbp", $this->trk_income_gbp);
			$statement->bindValue("trk_income_usdgbp", $this->trkincomeusdgbp);
			$statement->bindValue("trk_income_usd", $this->trkincomeusd);
			$statement->bindValue("trk_income_fee_transfer_paypal_usd", $this->trk_income_fee_transfer_paypal_usd);
			$statement->bindValue("trk_income_fee_transfer_paypal_gbp", $this->trk_income_fee_transfer_paypal_gbp);
			$statement->bindValue("trk_income_fee_porc_nequi", $this->trk_income_fee_porc_nequi);
			$statement->bindValue("trk_income_iva_porc_comision_nequi", $this->trk_income_iva_porc_comision_nequi);
			$statement->bindValue("trk_income_trm_nequi", $this->trk_income_trm_nequi);
			$statement->bindValue(":id_config", 1);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modificar_trebol(PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_bt_bank = :$cq->bd_bt_bank ";
			$query .= "  ,$cq->bd_bt_porc_apuesta_min = :$cq->bd_bt_porc_apuesta_min ";
			$query .= "  ,$cq->bd_bt_min_porc_profit = :$cq->bd_bt_min_porc_profit ";
			$query .= "  ,$cq->bd_bt_min_porc_bank = :$cq->bd_bt_min_porc_bank ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_bt_bank", $this->bt_bank);
			$statement->bindValue(":$cq->bd_bt_porc_apuesta_min", $this->bt_porc_apuesta_min);
			$statement->bindValue(":$cq->bd_bt_min_porc_profit", $this->bt_min_porc_profit);
			$statement->bindValue(":$cq->bd_bt_min_porc_bank", $this->bt_min_porc_bank);
			$statement->bindValue(":$cq->bd_id", 1);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modifyConfigApuestas($conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_valmin_valor_apuesta = :$cq->bd_valmin_valor_apuesta ";
			$query .= "  ,$cq->bd_betbankroll = :$cq->bd_betbankroll ";
			$query .= "  ,$cq->bd_riskkellyformula = :$cq->bd_riskkellyformula ";
			$query .= "  ,$cq->bd_valmin_prediccion = :$cq->bd_valmin_prediccion ";
			$query .= "  ,$cq->bd_fecha_revision_betplay = :$cq->bd_fecha_revision_betplay ";
			$query .= "  ,$cq->bd_riesgo_diario = :$cq->bd_riesgo_diario ";
			$query .= "  ,$cq->bd_val_min_avg_all = :$cq->bd_val_min_avg_all ";
			$query .= "  ,$cq->bd_val_min_avg_specific = :$cq->bd_val_min_avg_specific ";
			$query .= "  ,$cq->bd_val_penalidad_mitad_ultimos_partidos = :$cq->bd_val_penalidad_mitad_ultimos_partidos ";
			$query .= "  ,$cq->bd_val_penalidad_resto_ultimos_partidos = :$cq->bd_val_penalidad_resto_ultimos_partidos ";
			$query .= "  ,$cq->bd_val_penalidad_same_fixture = :$cq->bd_val_penalidad_same_fixture ";
			$query .= "  ,$cq->bd_val_penalidad_1ro_ultimos_partidos = :$cq->bd_val_penalidad_1ro_ultimos_partidos ";
			$query .= "  ,$cq->bd_val_penalidad_2do_ultimos_partidos = :$cq->bd_val_penalidad_2do_ultimos_partidos ";
			$query .= "  ,$cq->bd_val_penalidad_3ro_ultimos_partidos = :$cq->bd_val_penalidad_3ro_ultimos_partidos ";
			$query .= "  ,$cq->bd_min_perc_kelly_crit = :$cq->bd_min_perc_kelly_crit ";
			$query .= "  ,$cq->bd_min_odds = :$cq->bd_min_odds ";
			$query .= "  ,$cq->bd_perc_penalidad_avg_ult_partidos = :$cq->bd_perc_penalidad_avg_ult_partidos ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_valmin_valor_apuesta", $this->valmin_valor_apuesta);
			$statement->bindValue(":$cq->bd_betbankroll", $this->betbankroll);
			$statement->bindValue(":$cq->bd_riskkellyformula", $this->riskkellyformula);
			$statement->bindValue(":$cq->bd_valmin_prediccion", $this->valmin_prediccion);
			$statement->bindValue(":$cq->bd_fecha_revision_betplay", $this->fecha_revision_betplay);
			$statement->bindValue(":$cq->bd_riesgo_diario", $this->riesgo_diario);
			$statement->bindValue(":$cq->bd_val_min_avg_all", $this->val_min_avg_all);
			$statement->bindValue(":$cq->bd_val_min_avg_specific", $this->val_min_avg_specific);
			$statement->bindValue(":$cq->bd_val_penalidad_mitad_ultimos_partidos", $this->val_penalidad_mitad_ultimos_partidos);
			$statement->bindValue(":$cq->bd_val_penalidad_resto_ultimos_partidos", $this->val_penalidad_resto_ultimos_partidos);
			$statement->bindValue(":$cq->bd_val_penalidad_same_fixture", $this->val_penalidad_same_fixture);
			$statement->bindValue(":$cq->bd_val_penalidad_1ro_ultimos_partidos", $this->val_penalidad_1ro_ultimos_partidos);
			$statement->bindValue(":$cq->bd_val_penalidad_2do_ultimos_partidos", $this->val_penalidad_2do_ultimos_partidos);
			$statement->bindValue(":$cq->bd_val_penalidad_3ro_ultimos_partidos", $this->val_penalidad_3ro_ultimos_partidos);
			$statement->bindValue(":$cq->bd_min_perc_kelly_crit", $this->min_perc_kelly_crit);
			$statement->bindValue(":$cq->bd_min_odds", $this->min_odds);
			$statement->bindValue(":$cq->bd_perc_penalidad_avg_ult_partidos", $this->perc_penalidad_avg_ult_partidos);
			$statement->bindValue(":$cq->bd_id", 1);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	
	/**
	 * @throws Exception
	 */
	function save($conexion): void
	{
		try {
			$query = 'UPDATE configs SET trm = :trm, trk_id = :trk_id, trk_meta_diaria = :trk_meta_diaria, trk_meta_semanal = :trk_meta_semanal, trk_meta_mensual = :trk_meta_mensual, trk_fee = :trk_fee, bet_valor_apostado_default = :bet_valor_apostado_default, bet_season_1 = :bet_season_1, bet_season_2 = :bet_season_2 WHERE id_config = 1';

			$statement = $conexion->prepare($query);
			$params = array(
				                    ':trm'                           => $this->trm,
				                    ':trk_id'                        => $this->trk_id,
				                    ':trk_meta_diaria'               => $this->trk_meta_diaria,
				                    ':trk_meta_semanal'              => $this->trk_meta_semanal,
				                    ':trk_meta_mensual'              => $this->trk_meta_mensual,
				                    ':trk_fee'                       => $this->trk_fee,
				                    ':bet_valor_apostado_default'    => $this->bet_valor_apostado_default,
				                    ':bet_season_1'                  => $this->bet_season_1,
				                    ':bet_season_2'                  => $this->bet_season_2
			                );
			$statement->execute($params);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function convertCOPMinitoUSD($valor, $trm, $round): float
	{
		try {
			return round(($valor * 1000) / $trm, $round);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateTotal(): void
	{
		try {
			$this->total = $this->trkincomeusd + $this->trkincomeusdgbp;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateTotalProyectado(): void
	{
		try {
			$this->totalproyectado = $this->trkincomeusd + $this->trkincomeusdgbp + $this->trkincomeproyectadousdgbp + $this->trkincomeproyectadousd;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateValFee(): void
	{
		try {
			$this->valfeefrank = round($this->total * ($this->porcfeefrank / 100), 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateValFeeProyectado(): void
	{
		try {
			$this->valfeefrankproyectado = round($this->totalproyectado * ($this->porcfeefrank / 100), 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateProfit(): void
	{
		try {
			$this->valprofit = $this->total - $this->valfeefrank;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateProfitProyectado(): void
	{
		try {
			$this->valprofitproyectado = $this->totalproyectado - $this->valfeefrankproyectado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateNetProfit(): void
	{
		try {
			$this->valnetprofit = $this->total - $this->valfeefrank - $this->feetransfer;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateNetProfitProyectado(): void
	{
		try {
			$this->valnetprofitproyectado = $this->totalproyectado - $this->valfeefrankproyectado - $this->feetransfer;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateProfitCop(): void
	{
		try {
			$this->valprofitcop = formatNumberThousandsToHundreds($this->valnetprofit * $this->trm);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calculateProfitCopProyectado(): void
	{
		try {
			$this->valprofitcopproyectado = formatNumberThousandsToHundreds($this->valnetprofitproyectado * $this->trm);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_trk_income_paypal($paramref): void
	{
		try {
			$sin_deuda = $paramref['sin_deuda'];
			$sin_fee   = $paramref['sin_fee'];
			
			$this->trk_income_total_acumulado = $this->trkincomeusdgbp + $this->trkincomeusd;
			
			if ($sin_deuda == 1) {
				$this->trk_income_total_acumulado_menos_deuda = $this->trk_income_total_acumulado;
				
			} else {
				$this->trk_income_total_acumulado_menos_deuda = $this->trk_income_total_acumulado - $this->trk_income_deuda;
			}
			if ($sin_fee == 1) {
				$this->valfeefrank = 0;
				
			} else {
				$this->valfeefrank = round($this->trk_income_total_acumulado_menos_deuda * ($this->porcfeefrank / 100), 2);
			}
			
			$this->trk_income_total_acumulado_menos_fee        = $this->trk_income_total_acumulado_menos_deuda - $this->valfeefrank;
			$this->trk_income_fee_transfer_paypal_total        += $this->trk_income_fee_transfer_paypal_usd;
			$this->trk_income_total_acumulado_menos_fee_paypal = $this->trk_income_total_acumulado_menos_fee - $this->trk_income_fee_transfer_paypal_total;
			$this->trk_income_total_acumulado_cop              = round($this->trk_income_total_acumulado_menos_fee_paypal * $this->trk_income_trm_nequi);
			$this->trk_income_fee_valor_nequi                  = $this->trk_income_total_acumulado_cop * ($this->trk_income_fee_porc_nequi / 100);
			$this->trk_income_iva_valor_comision_nequi         = $this->trk_income_fee_valor_nequi * ($this->trk_income_iva_porc_comision_nequi / 100);
			$this->trk_income_total_acumulado_cop_menos_nequi  = $this->trk_income_total_acumulado_cop - ($this->trk_income_fee_valor_nequi + $this->trk_income_iva_valor_comision_nequi);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
}

?>