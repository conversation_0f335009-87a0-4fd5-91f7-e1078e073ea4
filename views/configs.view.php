<?php
#region region DOCS
/** @var Config $config */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en"  class="dark-mode">
<head>
	<title>My Dash | Trk income</title>
	<meta content="" name="description" />
	<meta content="" name="author" />

    <!-- #head -->
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>

    <!-- ================== BEGIN page-css ================== -->
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet" />
    <!-- ================== END page-css ================== -->
</head>
<body>
    <!-- #loader -->
    <?php require_once __ROOT__ . '/views/loader.view.php'; ?>

    <!-- BEGIN #app -->
    <div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
        <!-- #header -->
        <?php require_once __ROOT__ . '/views/header.view.php'; ?>

        <!-- #sidebar -->
        <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

        <!-- BEGIN #content -->
		<div id="content" class="app-content">
            <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

            <!-- BEGIN breadcrumb -->
            <ol class="breadcrumb float-xl-end">
                <li class="breadcrumb-item"><a href="<?php echo htmlspecialchars(RUTA) ?>dashboard">Dashboard</a></li>
                <li class="breadcrumb-item active"><a href="javascript:;">Configs</a></li>
            </ol>
            <!-- END breadcrumb -->

            <!-- BEGIN page-header -->
            <h1 class="page-header">Configs</h1>
            <!-- END page-header -->

            <form action="configs" method="POST">
                <div class="row">
                    <div class="col">
                        <div class="form-floating">
                            <input type="text" class="form-control fs-15px" name="trm" id="trm" value="<?php echo limpiar_datos($config->trm) ?>" />
                            <label for="trm" class="d-flex align-items-center fs-15px">
                                TRM
                            </label>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-floating">
                            <input type="text" class="form-control fs-15px" name="trk_id" id="trk_id" value="<?php echo limpiar_datos($config->trk_id) ?>" />
                            <label for="trk_id" class="d-flex align-items-center fs-15px">
                                TRK ID:
                            </label>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-floating">
                            <input type="number" step="0.01" class="form-control fs-15px" name="trk_fee" id="trk_fee" value="<?php echo limpiar_datos($config->trk_fee) ?>" />
                            <label for="trk_fee" class="d-flex align-items-center fs-15px">
                                Fee (Comission) TRK
                            </label>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col">
                        <div class="form-floating">
                            <input type="number" step="0.01" class="form-control fs-15px" name="trk_meta_diaria" id="trk_meta_diaria" value="<?php echo limpiar_datos($config->trk_meta_diaria) ?>" />
                            <label for="trk_meta_diaria" class="d-flex align-items-center fs-15px">
                                Meta diaria TRK
                            </label>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-floating">
                            <input type="number" step="0.01" class="form-control fs-15px" name="trk_meta_semanal" id="trk_meta_semanal" value="<?php echo limpiar_datos($config->trk_meta_semanal) ?>" />
                            <label for="trk_meta_semanal" class="d-flex align-items-center fs-15px">
                                Meta semanal TRK
                            </label>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-floating">
                            <input type="number" step="0.01" class="form-control fs-15px" name="trk_meta_mensual" id="trk_meta_mensual" value="<?php echo limpiar_datos($config->trk_meta_mensual) ?>" />
                            <label for="trk_meta_mensual" class="d-flex align-items-center fs-15px">
                                Meta mensual TRK
                            </label>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col">
                        <div class="form-floating">
                            <input type="number" step="0.01" class="form-control fs-15px" name="bet_valor_apostado_default" id="bet_valor_apostado_default" value="<?php echo limpiar_datos($config->bet_valor_apostado_default) ?>" />
                            <label for="bet_valor_apostado_default" class="d-flex align-items-center fs-15px">
                                Default valor apostado
                            </label>
                        </div>
                    </div>
                </div>


                <!-- Season configuration section separator -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr class="my-3">
                        <h5 class="mb-3">Configuración de Temporadas</h5>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-floating">
                            <input type="text" class="form-control fs-15px" name="bet_season_1" id="bet_season_1" value="<?php echo limpiar_datos($config->bet_season_1 ?? '') ?>" />
                            <label for="bet_season_1" class="d-flex align-items-center fs-15px">Bet Season 1</label>
                        </div>
                        <small class="form-text text-muted">Especificar el season en formato XXXX/XX (e.g., 2024/25)</small>
                    </div>
                    <div class="col">
                        <div class="form-floating">
                            <input type="text" class="form-control fs-15px" name="bet_season_2" id="bet_season_2" value="<?php echo limpiar_datos($config->bet_season_2 ?? '') ?>" />
                            <label for="bet_season_2" class="d-flex align-items-center fs-15px">Bet Season 2</label>
                        </div>
                        <small class="form-text text-muted">Especificar season en formato XXXX (e.g., 2024)</small>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col">
                        <button type="submit" id="sub_save" name="sub_save" class="btn btn-success w-100">
                            Save
                        </button>
                    </div>
                </div>

            </form>
        </div>
		<!-- END #content -->

        <!-- #scrolltop -->
        <?php require_once __ROOT__ . '/views/scrolltop.view.php'; ?>
    </div>
    <!-- END #app -->

    <!-- #js -->
    <?php require_once __ROOT__ . '/views/js.view.php'; ?>
    <?php require_once __ROOT__ . '/views/datepickerjs.view.php'; ?>

</body>
</html>